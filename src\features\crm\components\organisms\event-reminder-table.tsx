import { Config<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Button, Tooltip, Space } from "antd";
import type { TableProps } from "antd";
import { useMemo, useCallback } from "react";
import { EventReminder, EventReminderStatus } from "../../types/event-reminder";
import { formatDateTime } from "@lib/helpers";
import {
    Hash,
    Mail,
    MessageCircle,
    ExternalLink,
    RotateCcw,
    TriangleAlert,
} from "lucide-react";
import {
    useRetryEmailInvitation,
    useRetryWhatsappInvitation,
} from "../../hooks/use-event-reminder";
import { RetrieveEventSchedule } from "../../types/event-schedule";

const { Text, Link } = Typography;

const getStatusColor = (status: EventReminderStatus) => {
    switch (status) {
        case EventReminderStatus.SENT:
            return "success";
        case EventReminderStatus.PENDING:
            return "processing";
        case EventReminderStatus.FAILED:
            return "error";
        case EventReminderStatus.RETRYING:
            return "warning";
        case EventReminderStatus.CANCELLED:
            return "default";
        default:
            return "default";
    }
};

const getStatusText = (status: EventReminderStatus) => {
    switch (status) {
        case EventReminderStatus.SENT:
            return "Enviado";
        case EventReminderStatus.PENDING:
            return "Pendiente";
        case EventReminderStatus.FAILED:
            return "Fallido";
        case EventReminderStatus.RETRYING:
            return "Reintentando";
        case EventReminderStatus.CANCELLED:
            return "Cancelado";
        default:
            return status;
    }
};

type EventReminderTableProps = {
    eventSchedule: RetrieveEventSchedule;
    reminders: EventReminder[];
    loading?: boolean;
};

export default function EventReminderTable({
    eventSchedule,
    reminders,
    loading = false,
}: EventReminderTableProps) {
    const retryEmailMutation = useRetryEmailInvitation();
    const retryWhatsappMutation = useRetryWhatsappInvitation();

    const handleRetryEmail = useCallback(
        (rid: string) => {
            retryEmailMutation.mutate(rid);
        },
        [retryEmailMutation],
    );

    const handleRetryWhatsapp = useCallback(
        (rid: string) => {
            retryWhatsappMutation.mutate(rid);
        },
        [retryWhatsappMutation],
    );

    const columns: TableProps<EventReminder>["columns"] = useMemo(
        () => [
            {
                title: "ID",
                dataIndex: "rid",
                key: "rid",
                width: 120,
                render: (rid: string) => (
                    <div className="flex items-center gap-1">
                        <Hash size={14} strokeWidth={2.5} className="text-gray-500" />
                        <Text className="font-semibold text-sm">#{rid.slice(-8)}</Text>
                    </div>
                ),
            },
            {
                title: "INFORMACIÓN DE CONTACTO",
                key: "contact",
                width: 250,
                render: (_, record: EventReminder) => (
                    <div className="space-y-1">
                        <div className="font-semibold text-sm">
                            {record.enrollment.fullName}
                        </div>
                        <div className="text-xs text-gray-600">
                            {record.enrollment.email}
                        </div>
                        <div className="text-xs text-gray-600">
                            {record.enrollment.phoneNumber}
                        </div>
                        {record.enrollment.user && (
                            <Badge
                                size="small"
                                status="success"
                                text="Usuario registrado"
                            />
                        )}
                    </div>
                ),
            },
            {
                title: "TEMPLATE WHATSAPP",
                key: "whatsappTemplate",
                width: 200,
                render: (_, record: EventReminder) => (
                    <div className="space-y-1">
                        {record.whatsappTemplate?.extReference ? (
                            <>
                                <Link
                                    href={`https://crm.tokechat.net/flows/${record.whatsappTemplate.extReference}`}
                                    target="_blank"
                                    className="flex items-center gap-1 text-sm"
                                >
                                    {record.whatsappTemplate.name}
                                    <ExternalLink size={12} />
                                </Link>
                                <div className="text-xs text-gray-500">
                                    {record.whatsappTemplate.extReference
                                        ? `Ref. CRM TokeChat (${record.whatsappTemplate.extReference})`
                                        : ""}
                                </div>
                            </>
                        ) : (
                            <Text type="secondary" className="flex items-center gap-1">
                                <TriangleAlert
                                    size={12}
                                    className="text-state-yellow-full"
                                />
                                No se ha seleccionado la plantilla
                            </Text>
                        )}
                    </div>
                ),
            },
            {
                title: "ESTADO EMAIL",
                key: "emailStatus",
                width: 150,
                render: (_, record: EventReminder) => (
                    <div className="space-y-2">
                        <Badge
                            status={getStatusColor(record.statusEmail)}
                            text={getStatusText(record.statusEmail)}
                        />
                        {record.sentAtEmail && (
                            <div className="text-xs text-gray-500">
                                {formatDateTime(record.sentAtEmail)}
                            </div>
                        )}
                        {record.lastErrorEmail && (
                            <Tooltip title={record.lastErrorEmail}>
                                <div className="text-xs text-red-500 truncate max-w-32">
                                    {record.lastErrorEmail}
                                </div>
                            </Tooltip>
                        )}
                    </div>
                ),
            },
            {
                title: "ESTADO WHATSAPP",
                key: "whatsappStatus",
                width: 150,
                render: (_, record: EventReminder) => (
                    <div className="space-y-2">
                        {eventSchedule.isWhatsappActive ? (
                            <>
                                <Badge
                                    status={getStatusColor(record.statusWhatsapp)}
                                    text={getStatusText(record.statusWhatsapp)}
                                />
                                {record.sentAtWhatsapp && (
                                    <div className="text-xs text-gray-500">
                                        {formatDateTime(record.sentAtWhatsapp)}
                                    </div>
                                )}
                                {record.lastErrorWhatsapp && (
                                    <Tooltip title={record.lastErrorWhatsapp}>
                                        <div className="text-xs text-red-500 truncate max-w-32">
                                            {record.lastErrorWhatsapp}
                                        </div>
                                    </Tooltip>
                                )}
                            </>
                        ) : (
                            <Badge status="default" text="Desactivado" />
                        )}
                    </div>
                ),
            },
            {
                title: "ACCIONES",
                key: "actions",
                width: 150,
                render: (_, record: EventReminder) => (
                    <Space direction="vertical" size="small">
                        {record.statusEmail === EventReminderStatus.FAILED && (
                            <Button
                                size="small"
                                icon={<Mail size={14} />}
                                onClick={() => handleRetryEmail(record.rid)}
                                loading={retryEmailMutation.isPending}
                                className="flex items-center gap-1"
                            >
                                <RotateCcw size={12} />
                                Reintentar Email
                            </Button>
                        )}
                        {record.statusWhatsapp === EventReminderStatus.FAILED && (
                            <Button
                                size="small"
                                icon={<MessageCircle size={14} />}
                                onClick={() => handleRetryWhatsapp(record.rid)}
                                loading={retryWhatsappMutation.isPending}
                                className="flex items-center gap-1"
                            >
                                <RotateCcw size={12} />
                                Reintentar WhatsApp
                            </Button>
                        )}
                    </Space>
                ),
            },
        ],
        [
            retryEmailMutation.isPending,
            retryWhatsappMutation.isPending,
            handleRetryEmail,
            handleRetryWhatsapp,
            eventSchedule.isWhatsappActive,
        ],
    );

    return (
        <ConfigProvider
            theme={{
                components: {
                    Table: {
                        headerBg: "#FBFCFD",
                        borderColor: "#fff",
                        headerSplitColor: "#fafafa",
                        headerBorderRadius: 8,
                        rowHoverBg: "#F6FAFD",
                        rowSelectedBg: "#F6FAFD",
                        rowSelectedHoverBg: "#F6FAFD",
                        footerBg: "#F1F1F1",
                    },
                },
            }}
        >
            <Table
                className="rounded-lg shadow-sm"
                footer={() => ""}
                pagination={false}
                columns={columns}
                dataSource={reminders}
                loading={loading}
                rowKey={(record) => record.rid}
                scroll={{ x: 1400 }}
                locale={{
                    emptyText: (
                        <div className="text-center py-8">
                            <Text type="secondary">
                                No hay recordatorios para mostrar
                            </Text>
                        </div>
                    ),
                }}
            />
        </ConfigProvider>
    );
}
