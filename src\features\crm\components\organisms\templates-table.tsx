import { Template } from "../../types/template";
import { useEffect, useMemo, useState } from "react";
import {
    Button,
    Checkbox,
    ConfigProvider,
    Dropdown,
    Empty,
    Form,
    Input,
    Modal,
    Pagination,
    Popover,
    Table,
    Tag,
    Typography,
} from "antd";
import type { TableProps } from "antd";
import { useMutation } from "@tanstack/react-query";
import { Link, useSearchParams } from "react-router-dom";

const { Text } = Typography;
const { Search } = Input;

import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import Spinner from "@components/shared/atoms/Spinner";
// import { createTemplate, listTemplate, deleteTemplate } from "@services/portals/crm/template";

import Import from "@assets/icons/huge/import.svg?react";
import Plus from "@assets/icons/general/plus-white.svg?react";
import Reload from "@assets/icons/huge/reload.svg?react";
import Settings from "@assets/icons/huge/settings.svg?react";
import Trash from "@assets/icons/huge/trash-white.svg?react";
import DeleteStroke from "@assets/icons/huge/delete-stroke.svg?react";
import EditStroke from "@assets/icons/huge/edit-stroke.svg?react";
import MoreVertical from "@assets/icons/huge/more-vertical.svg?react";

import { formatDateTime } from "@lib/helpers";
import {
    CreateTemplateBody,
    CreateTemplateFormValues,
    Template,
    TemplateStatus,
    TemplateStatusLabel,
} from "@/features/crm/types/template";
import {
    createTemplate,
    deleteTemplate,
    listTemplate,
} from "@/features/crm/services/portals/template";
import { AxiosError } from "axios";
import { useApiError } from "@hooks/use-api-error";
import CrmLayout from "@/features/crm/layout";
import { useTemplates } from "../../hooks/use-template";

type TemplatesTableProps = {
    initialData: Template[];
};

const PAGE_SIZE = 10;

const INITIAL_CHECKED_VALUES = ["name", "category", "status", "createdAt"];

const INITIAL_COLUMNS: TableProps<Template>["columns"] = [
    {
        title: "NOMBRE",
        dataIndex: "name",
        key: "name",
        render: (name: string, record: Template) => (
            <Link to={`${record.tid}`} className="text-blue-full font-medium underline">
                {name}
            </Link>
        ),
    },
    {
        title: "ESTADO",
        dataIndex: "status",
        key: "status",
        render: (status: TemplateStatus) => (
            <Tag
                bordered={false}
                color={status === TemplateStatus.APPROVED ? "green" : "volcano"}
                className="rounded-full px-3"
            >
                {TemplateStatusLabel[status]}
            </Tag>
        ),
    },
    {
        title: "ÚLTIMA ACTUALIZACIÓN",
        dataIndex: "updatedAt",
        key: "updatedAt",
        render: (updatedAt: string) => {
            const formattedDate = formatDateTime(updatedAt);
            return <Text>{formattedDate}</Text>;
        },
    },
    {
        title: "FECHA DE CREACIÓN",
        dataIndex: "createdAt",
        key: "createdAt",
        render: (createdAt: string) => {
            const formattedDate = formatDateTime(createdAt);
            return <Text>{formattedDate}</Text>;
        },
    },
];

export default function TemplatesTable({ initialData }: TemplatesTableProps) {
    const [searchParams, setSearchParams] = useSearchParams();

    const DEFAULT_PAGE = searchParams.get("page")
        ? Number(searchParams.get("page"))
        : 1;

    const [checkedValues, setCheckedValues] =
        useState<string[]>(INITIAL_CHECKED_VALUES);

    // const handleShowHideColumns = (checkedValues: string[]) => {
    //     setCheckedValues(checkedValues);
    // };

    const [currentPage, setCurrentPage] = useState(DEFAULT_PAGE);

    const {
        isLoading,
        templates: data,
        COUNT: TOTAL_COUNT,
        refetch,
    } = useTemplates({ enabled: !initialData });

    const Templates = data;

    useEffect(() => {
        refetch();
    }, [currentPage, refetch]);

    const tableColumns = useMemo(() => {
        return (INITIAL_COLUMNS ?? []).filter((column) =>
            checkedValues.includes(column.key as string),
        );
    }, [checkedValues]);

    const defaultColumn = {
        title: "ACCIONES",
        key: "actions",
        render: (record: Template) => (
            <Dropdown
                trigger={["click"]}
                menu={{
                    items: [
                        {
                            key: "edit",
                            label: (
                                <>
                                    <div className="flex items-center gap-2 text-blue-full">
                                        <EditStroke className="w-5 h-5" /> Editar
                                    </div>
                                </>
                            ),
                        },
                        {
                            key: "delete",
                            label: (
                                <>
                                    <div className="flex items-center gap-2 text-state-red-full">
                                        <DeleteStroke className="w-5 h-5" /> Eliminar
                                    </div>
                                </>
                            ),
                        },
                    ],
                    onClick: ({ key }) => {
                        handleRowAction(key, record);
                    },
                }}
                placement="bottomRight"
            >
                <Button
                    icon={<MoreVertical className="w-5 h-5" />}
                    type="text"
                    size="small"
                />
            </Dropdown>
        ),
    };

    const rowSelection = {
        selectedRowKeys,
        onChange: (newSelectedRowKeys: React.Key[]) => {
            setSelectedRowKeys(newSelectedRowKeys);
        },
    };

    const handleBulkDelete = () => {
        if (selectedRowKeys.length === 0) return;

        Modal.confirm({
            title: "¿Está seguro de eliminar las plantillas seleccionadas?",
            content: `Se eliminarán ${selectedRowKeys.length} plantillas.`,
            okText: "Eliminar",
            okButtonProps: { danger: true },
            cancelText: "Cancelar",
            onOk() {
                deleteTemplateMutation.mutate(selectedRowKeys as string[]);
            },
        });
    };

    const handleRowAction = (key: string, record: Template) => {
        switch (key) {
            case "delete":
                Modal.confirm({
                    title: "¿Está seguro que desea eliminar esta plantilla?",
                    content: `La plantilla "${record.name}" será eliminada permanentemente.`,
                    okText: "Eliminar",
                    okButtonProps: { danger: true },
                    cancelText: "Cancelar",
                    onOk() {
                        deleteTemplateMutation.mutate([record.tid]);
                    },
                });
                break;
            case "edit":
                // Existing edit logic or placeholder
                console.info("Edit template", record);
                break;
        }
    };

    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

    // Delete mutation for single and bulk delete
    const { handleError } = useApiError({
        title: "Error al eliminar la plantilla",
    });

    const deleteTemplateMutation = useMutation({
        mutationFn: (ids: string[]) => {
            // Assuming deleteTemplate can handle single or multiple ids
            return Promise.all(ids.map((id) => deleteTemplate(id)));
        },
        onSuccess: () => {
            refetch(); // Refresh the list
            setSelectedRowKeys([]); // Clear selection
            Modal.success({
                title: "Eliminación exitosa",
                content: "Las plantillas han sido eliminadas correctamente.",
            });
        },
        onError: (error: AxiosError) => {
            handleError(error);
        },
    });

    return (
        <ConfigProvider
            theme={{
                components: {
                    Table: {
                        headerBg: "#FBFCFD",
                        borderColor: "#fff",
                        headerSplitColor: "#fafafa",
                        headerBorderRadius: 8,
                        rowHoverBg: "#F6FAFD",
                        rowSelectedBg: "#F6FAFD",
                        rowSelectedHoverBg: "#F6FAFD",
                        footerBg: "#F1F1F1",
                    },
                    Pagination: {
                        itemSize: 36,
                        itemActiveBg: "#F6FAFD",
                    },
                },
            }}
        >
            <Table
                rowSelection={rowSelection}
                locale={{
                    emptyText: <>{isLoading ? <Spinner /> : <Empty />}</>,
                }}
                columns={tableColumns ? [...tableColumns, defaultColumn] : []}
                dataSource={Templates}
                className="rounded-lg"
                footer={() => ""}
                pagination={false}
            />
            <div className="flex justify-between">
                <div className="flex items-center gap-3">
                    <Button
                        danger
                        type="primary"
                        size="large"
                        icon={<Trash />}
                        disabled={selectedRowKeys.length === 0}
                        onClick={handleBulkDelete}
                    >
                        Eliminar ({selectedRowKeys.length})
                    </Button>
                </div>
                <div>
                    <Pagination
                        defaultCurrent={DEFAULT_PAGE}
                        total={TOTAL_COUNT}
                        pageSize={PAGE_SIZE}
                        current={currentPage}
                        onChange={(page) => {
                            setCurrentPage(page);
                            const searchParams = new URLSearchParams();
                            searchParams.set("page", page.toString());
                            setSearchParams(searchParams);
                        }}
                    />
                </div>
            </div>
        </ConfigProvider>
    );
}
