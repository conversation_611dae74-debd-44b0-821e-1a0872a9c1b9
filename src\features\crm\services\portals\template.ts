import { PaginatedResponse } from "@myTypes/base";
import {
    CreateTemplateBody,
    Template,
    PartialUpdateTemplateBody,
} from "@myTypes/template";
import { portalsApi } from "@services/portals";

const DEFAULT_PAGE_SIZE = 10;
const DEFAULT_PAGE = 1;

export type ListTemplateQuery = {
    page?: number;
    pageSize?: number;
    search?: string;
    sortBy?: string;
    order?: "asc" | "desc";
    name?: string;
};

export const listTemplate = async (
    params: ListTemplateQuery = {
        page: DEFAULT_PAGE,
        pageSize: DEFAULT_PAGE_SIZE,
    },
): Promise<PaginatedResponse<Template>> => {
    const response = await portalsApi.get("crm/templates", {
        params,
    });
    return response.data;
};

export const listApprovedTemplates = async () => {
    const response = await portalsApi.get("crm/templates/approved");
    return response.data;
};

export const createTemplate = async (data: CreateTemplateBody) => {
    const response = await portalsApi.post("crm/templates", data);
    return response.data;
};

export const retrieveTemplate = async (tid: string): Promise<Template> => {
    const response = await portalsApi.get(`crm/templates/${tid}`);
    return response.data;
};

export const deleteTemplate = async (tid: string) => {
    const response = await portalsApi.delete(`crm/templates/${tid}`);
    return response.data;
};

export const updateTemplate = async (tid: string, data: PartialUpdateTemplateBody) => {
    const formData = new FormData();
    const { headerImageFile, positionalParamsExample, buttons, ...payload } = data;

    Object.entries(payload).forEach(([key, value]) => {
        if (value) {
            formData.append(key, value as string);
        }
    });

    if (headerImageFile) {
        formData.append("headerImageFile", headerImageFile[0] as unknown as Blob);
    }

    if (positionalParamsExample) {
        formData.append(
            "positionalParamsExample",
            JSON.stringify(positionalParamsExample),
        );
    }

    if (buttons) {
        formData.append("buttons", JSON.stringify(buttons));
    }

    const response = await portalsApi.patch(`crm/templates/${tid}`, formData, {
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });
    return response.data;
};

export const sendTemplateToMeta = async (tid: string) => {
    const response = await portalsApi.post(`crm/templates/${tid}/send-to-meta`);
    return response.data;
};

export const syncTemplates = async () => {
    const response = await portalsApi.post("crm/templates/sync-templates");
    return response.data;
};
