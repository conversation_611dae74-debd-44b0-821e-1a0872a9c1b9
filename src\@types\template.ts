import { UploadFile } from "antd";

/**
 *  DRAFT = "DRAFT"
    IN_REVIEW = "IN_REVIEW"
    REJECTED = "REJECTED"
    APPROVED = "APPROVED"
    PAUSED = "PAUSED"
    DISABLED = "DISABLED"

 */
export enum TemplateStatus {
    DRAFT = "DRAFT",
    IN_REVIEW = "IN_REVIEW",
    APPROVED = "APPROVED",
    PAUSED = "PAUSED",
    DISABLED = "DISABLED",
    REJECTED = "REJECTED",
}

export const TemplateStatusLabel: Record<TemplateStatus, string> = {
    [TemplateStatus.DRAFT]: "Borrador",
    [TemplateStatus.IN_REVIEW]: "En revisión",
    [TemplateStatus.APPROVED]: "Aprobado",
    [TemplateStatus.PAUSED]: "Pausado",
    [TemplateStatus.DISABLED]: "Inactivo",
    [TemplateStatus.REJECTED]: "Rechazado",
};
export type TemplateButton = {
    type: "URL";
    text: string;
    url: string;
    urlType: "static" | "dynamic";
    example?: string[];
};
export type TemplateHeaderImage = {
    fid: string;
    name: string;
    url: string;
};

export type Template = {
    tid: string;
    name: string;
    status: TemplateStatus;
    headerImage?: TemplateHeaderImage | null;
    bodyText: string;
    positionalParamsExample?: string[] | null;
    buttons?: TemplateButton[] | null;
    extReference?: string | null;
    createdAt: string | Date;
    updatedAt: string | Date;
};

export type CreateTemplateFormValues = Partial<
    Omit<Template, "tid" | "status" | "createdAt" | "updatedAt">
>;

export type CreateTemplateBody = Partial<
    Omit<Template, "tid" | "createdAt" | "status" | "updatedAt">
>;

export type PartialUpdateTemplateBody = Partial<
    Omit<Template, "tid" | "key" | "createdAt" | "updatedAt">
> & {
    headerImageFile?: UploadFile[];
    deleteHeaderImage?: boolean;
};
