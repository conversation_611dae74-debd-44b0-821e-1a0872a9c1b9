import { Link, useNavigate, useParams } from "react-router-dom";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, Card, Image, Modal, Tag, Typography } from "antd";

const { Text, Title } = Typography;

import Edit from "@assets/icons/general/edit-stroke.svg?react";
import Trash from "@assets/icons/huge/trash-white.svg?react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
    deleteTemplate,
    retrieveTemplate,
    sendTemplateToMeta,
} from "@/features/crm/services/portals/template";
import { useEffect } from "react";
import { AxiosError } from "axios";
import Spinner from "@components/shared/atoms/Spinner";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import { TemplateStatus, TemplateStatusLabel } from "@myTypes/template";
import Sent from "@assets/icons/huge/sent.svg?react";
import CrmLayout from "@/features/crm/layout";
import WhatsAppMessagePreview from "@/features/crm/components/molecules/templates/WhatsappMessagePreview";

export default function TemplateDetailPage() {
    const navigate = useNavigate();
    const { tid } = useParams<{ tid: string }>();
    const [modal, contextHolder] = Modal.useModal();
    const queryClient = useQueryClient();

    const {
        isLoading,
        isError,
        data,
        error: fetchError,
    } = useQuery({
        queryKey: ["template", tid],
        queryFn: () => retrieveTemplate(tid as string),
        enabled: tid !== undefined,
    });

    const deleteMutation = useMutation({
        mutationFn: () => deleteTemplate(tid as string),
        onSuccess: () => {
            navigate("/crm/templates", { replace: true });
        },
        onError: (error: AxiosError) => {
            console.error(error);
        },
    });

    const sendMutation = useMutation({
        mutationFn: () => sendTemplateToMeta(tid as string),
        onSuccess: () => {
            modal.confirm({
                title: "Plantilla enviada",
                content:
                    "La plantilla ha sido enviada a Meta y se encuentra en revisión",
                okText: "Aceptar",
            });

            // invalidate the query
            queryClient.invalidateQueries({ queryKey: ["template", tid] });
        },
        onError: (error: AxiosError) => {
            const errorData = error.response?.data as { detail: string };
            modal.error({
                title: "Error al enviar la plantilla",
                content: errorData.detail || "Error al enviar la plantilla",
            });
        },
    });

    const highlightVariables = (text: string) => {
        return text.replace(
            /{{(\d+)}}/g,
            '<span style="background-color: #e6f4ff; padding: 2px 4px; border-radius: 4px; font-family: monospace;">{{$1}}</span>',
        );
    };

    useEffect(() => {
        if (isError) {
            const error = fetchError as AxiosError;
            if (error.response?.status === 404) {
                navigate("/crm/templates");
            }
        }
    }, [isError, fetchError, navigate]);

    const handleOnEdit = () => {
        navigate(`/crm/templates/${tid}/edit`);
    };

    const handleOnDelete = () => {
        modal.confirm({
            title: "¿Estás seguro de eliminar el presente registro?",
            content: "Esta acción no se puede deshacer",
            okText: "Eliminar",
            okButtonProps: { danger: true },
            cancelText: "Cancelar",
            onOk: () => deleteMutation.mutate(),
        });
    };

    const handleOnSend = () => {
        modal.confirm({
            title: "¿Estás seguro de enviar la plantilla?",
            content: "La aprobación de la plantilla demora a más tardar 24 horas.",
            okText: "Enviar",
            okButtonProps: { danger: true },
            cancelText: "Cancelar",
            onOk: () => {
                sendMutation.mutate();
            },
        });
    };

    const replaceVariables = (text: string, positionalParamsExample: string[]) => {
        return text.replace(/{{(\d+)}}/g, (_, number) => {
            return positionalParamsExample[number - 1];
        });
    };

    return (
        <>
            {contextHolder}
            <CrmLayout>
                <div className="w-full h-full space-y-5 max-w-7xl">
                    <div className="flex justify-between items-center">
                        <WelcomeBar helperText="Gestiona aquí las plantillas para mensajes de WhatsApp" />
                        <div className="flex gap-3">
                            {data?.status == TemplateStatus.DRAFT && (
                                <Button
                                    type="primary"
                                    size="large"
                                    style={{ fontSize: 16 }}
                                    icon={<Edit />}
                                    disabled={isError}
                                    onClick={handleOnEdit}
                                >
                                    Editar
                                </Button>
                            )}
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Trash />}
                                danger
                                disabled={isError}
                                onClick={handleOnDelete}
                            >
                                Eliminar
                            </Button>
                        </div>
                    </div>

                    {isLoading ? (
                        <Spinner />
                    ) : (
                        <>
                            <div className="p-5 bg-white-full rounded-lg space-y-5">
                                <div className="flex items-center justify-between">
                                    <Breadcrumb
                                        separator=">"
                                        items={[
                                            {
                                                title: (
                                                    <Link
                                                        to="/crm/templates"
                                                        className="text-base"
                                                    >
                                                        Plantillas
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Link
                                                        to={`/crm/templates/${tid}`}
                                                        className="text-base text-blue-medium"
                                                    >
                                                        {data?.name}
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Text className="text-base">
                                                        Detalle
                                                    </Text>
                                                ),
                                            },
                                        ]}
                                    />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 lg:grid-cols-3 gap-5">
                                {/* Información General */}
                                <div className="lg:col-span-2">
                                    <Card
                                        title="Información General"
                                        className="h-full"
                                    >
                                        <div className="space-y-6">
                                            <div>
                                                <Text className="text-sm text-gray-500">
                                                    Nombre de la plantilla
                                                </Text>
                                                <Title level={4} className="mt-1">
                                                    {data?.name}
                                                </Title>
                                            </div>

                                            {data?.headerImage && (
                                                <div>
                                                    <Text className="text-sm text-gray-500">
                                                        Imagen de cabecera
                                                    </Text>
                                                    <div className="mt-2">
                                                        <Image
                                                            src={data.headerImage.url}
                                                            alt={data.headerImage.name}
                                                            className="rounded-lg"
                                                        />
                                                    </div>
                                                </div>
                                            )}
                                            <div>
                                                <Text className="text-sm text-gray-500">
                                                    Contenido del mensaje
                                                </Text>
                                                <p
                                                    className="mt-2 text-base whitespace-pre-wrap"
                                                    dangerouslySetInnerHTML={{
                                                        __html: highlightVariables(
                                                            data?.bodyText || "",
                                                        ),
                                                    }}
                                                />
                                            </div>

                                            {data?.positionalParamsExample &&
                                                data?.positionalParamsExample.length >
                                                    0 && (
                                                    <div>
                                                        <Text className="text-sm text-gray-500">
                                                            Ejemplo de parámetros
                                                        </Text>
                                                        {data.positionalParamsExample.map(
                                                            (param, index) => (
                                                                <p
                                                                    key={index}
                                                                    className="mt-2 monospace"
                                                                    dangerouslySetInnerHTML={{
                                                                        __html: highlightVariables(
                                                                            `{{${index + 1}}} ${param}`,
                                                                        ),
                                                                    }}
                                                                />
                                                            ),
                                                        )}
                                                    </div>
                                                )}
                                            {data?.buttons && (
                                                <div className="flex flex-col">
                                                    <Text className="text-sm text-gray-500">
                                                        Botones
                                                    </Text>
                                                    {data?.buttons.length > 0 && (
                                                        <div className="flex flex-wrap gap-4">
                                                            {data?.buttons.map(
                                                                (button, index) => (
                                                                    <div
                                                                        className="py-2 mb-4"
                                                                        key={index}
                                                                    >
                                                                        <p>
                                                                            <span className="text-gray-500 mr-4">
                                                                                Texto
                                                                                del
                                                                                botón:
                                                                            </span>{" "}
                                                                            {
                                                                                button.text
                                                                            }
                                                                        </p>
                                                                        <p>
                                                                            <span className="text-gray-500 mr-4">
                                                                                Enlace
                                                                                del
                                                                                botón:
                                                                            </span>
                                                                            <Link
                                                                                className="text-blue-medium"
                                                                                target="_blank"
                                                                                to={
                                                                                    button.url
                                                                                }
                                                                            >
                                                                                {
                                                                                    button.url
                                                                                }
                                                                            </Link>
                                                                        </p>
                                                                        {button.example &&
                                                                            button
                                                                                .example
                                                                                .length >
                                                                                0 && (
                                                                                <p>
                                                                                    <span className="text-gray-500 mr-4">
                                                                                        Variables:
                                                                                    </span>
                                                                                    <span>
                                                                                        {button.example.join(
                                                                                            ", ",
                                                                                        )}
                                                                                    </span>
                                                                                </p>
                                                                            )}
                                                                    </div>
                                                                ),
                                                            )}
                                                        </div>
                                                    )}
                                                </div>
                                            )}
                                        </div>
                                    </Card>
                                </div>

                                {/* Vista Previa */}
                                <div className="flex flex-col gap-5">
                                    <div className="bg-white-full p-5 rounded-lg shadow-sm">
                                        <p className="text-gray-400 font-semibold text-sm">
                                            ESTADO DE LA PLANTILLA
                                        </p>
                                        <div className="mt-3 flex items-center justify-between">
                                            <Tag
                                                color={
                                                    data?.status ===
                                                    TemplateStatus.APPROVED
                                                        ? "green"
                                                        : data?.status ===
                                                            TemplateStatus.REJECTED
                                                          ? "red"
                                                          : data?.status ===
                                                              TemplateStatus.DRAFT
                                                            ? "blue"
                                                            : "orange"
                                                }
                                                className="py-2 px-6"
                                            >
                                                {
                                                    TemplateStatusLabel[
                                                        data?.status ||
                                                            TemplateStatus.DRAFT
                                                    ]
                                                }
                                            </Tag>
                                            {data?.status == TemplateStatus.DRAFT &&
                                                data.bodyText && (
                                                    <Button
                                                        type="primary"
                                                        icon={
                                                            <Sent className="w-3 h-3" />
                                                        }
                                                        disabled={
                                                            isError ||
                                                            sendMutation.isPending
                                                        }
                                                        onClick={handleOnSend}
                                                    >
                                                        Enviar
                                                    </Button>
                                                )}
                                        </div>
                                    </div>
                                    <div className="h-full">
                                        <div className="py-4 px-5 bg-white-full rounded-lg shadow-sm cursor-pointer">
                                            <p className="text-gray-400 font-semibold text-sm">
                                                VISTA PREVIA
                                            </p>
                                        </div>
                                        <WhatsAppMessagePreview
                                            imageUrl={data?.headerImage?.url}
                                            bodyText={replaceVariables(
                                                data?.bodyText || "",
                                                data?.positionalParamsExample || [],
                                            )}
                                            buttons={data?.buttons?.map(
                                                (button) => button.text,
                                            )}
                                        />
                                    </div>
                                </div>
                            </div>
                        </>
                    )}
                </div>
            </CrmLayout>
        </>
    );
}
