import LinkSquare from "@assets/icons/huge/link-square.svg?react";

interface WhatsAppMessagePreviewProps {
    imageUrl?: string
    bodyText: string
    buttons?: string[]
}

export default function WhatsAppMessagePreview({ imageUrl, bodyText, buttons }: WhatsAppMessagePreviewProps) {
    return (
        <div className="max-w-md p-4 bg-[#e5ddd5]">
            <div className="bg-white-full rounded-xl shadow-sm overflow-hidden">
                {imageUrl && (
                    <div className="relative bg-gray-100">
                        <img src={imageUrl || "/placeholder.svg"} alt="Preview image" className="w-full h-auto object-cover" />
                    </div>
                )}

                <div className="p-4">
                    <div className="space-y-4">
                        <p className="text-[15px] text-gray-800 whitespace-pre-line">{bodyText}</p>

                        {buttons && buttons.length > 0 && (
                            <div className="pt-2 border-t">
                                {buttons.map((button, index) => (
                                    <div
                                        key={index}
                                        className="w-full flex items-center justify-center"
                                    >
                                        <button className="flex items-center gap-2 text-center text-blue-500 font-medium px-6 py-2 hover:bg-blue-50">

                                            <LinkSquare
                                                className="w-4 h-4" />
                                            {button}
                                        </button>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>

                    <div className="text-right mt-2">
                        <span className="text-xs text-gray-500">9:09 pm</span>
                    </div>
                </div>
            </div>
        </div>
    )
}

