import { Button, Input, Radio, Space, Typography, Form } from 'antd';

import Plus from "@assets/icons/huge/plus.svg?react";
import Delete from "@assets/icons/huge/delete-stroke.svg?react";
import { TemplateButton } from '@myTypes/template';
import URLVariableInput from '../URLVariableInput';
const { Text } = Typography;


interface WhatsappButtonsEditorProps {
    value: TemplateButton[] | null;
    onChange: (value: TemplateButton[]) => void;
}
const WhatsappButtonsEditor = ({ value, onChange, }: WhatsappButtonsEditorProps) => {
    if (!value) value = []
    const handleAddButton = () => {
        if (value.length < 2) {
            onChange([
                ...value,
                {
                    type: 'URL',
                    text: '',
                    url: '',
                    urlType: 'static',
                    example: []
                }
            ]);
        }
    };

    const handleRemoveButton = (index: number) => {
        const newButtons = value.filter((_, i) => i !== index);
        onChange(newButtons);
    };

    const handleButtonChange = (index: number, field: keyof TemplateButton, newValue: string | string[]) => {
        const newButtons = value.map((button, i) => {
            if (i === index) {
                const updatedButton = { ...button, [field]: newValue };
                // Si cambiamos a URL estática, limpiamos el ejemplo
                if (field === 'urlType' && newValue === 'static') {
                    delete updatedButton.example;
                }
                return updatedButton;
            }
            return button;
        });
        onChange(newButtons);
    };

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <Text strong>Botones</Text>
                <Button
                    type="dashed"
                    icon={<Plus />}
                    onClick={handleAddButton}
                    disabled={value.length >= 2}
                >
                    Agregar botón
                </Button>
            </div>

            <div className="space-y-4">
                {value.map((button, index) => (
                    <div
                        key={index}
                        className="p-4 border rounded-lg bg-white-full space-y-3"
                    >
                        <div className="flex justify-between items-center">
                            <Text strong>Botón {index + 1}</Text>
                            <Button
                                type="text"
                                danger
                                icon={<Delete />}
                                onClick={() => handleRemoveButton(index)}
                            />
                        </div>

                        <Space direction="vertical" className="w-full">
                            <Form.Item
                                label="Texto del botón"
                                className="mb-0"
                                help={`${button.text?.length || 0}/25 caracteres`}
                            >
                                <Input
                                    value={button.text}
                                    onChange={(e) => handleButtonChange(index, 'text', e.target.value)}
                                    maxLength={25}
                                    placeholder="Ej: Visitar sitio web"
                                />
                            </Form.Item>

                            <Form.Item
                                label="Tipo de URL"
                                className="mb-0"
                            >
                                <Radio.Group
                                    value={button.urlType}
                                    defaultValue={button.urlType}
                                    onChange={(e) => handleButtonChange(index, 'urlType', e.target.value)}
                                >
                                    <Radio value="static">Estática</Radio>
                                    <Radio value="dynamic">Dinámica</Radio>
                                </Radio.Group>
                            </Form.Item>

                            <URLVariableInput
                                value={button.url}
                                onChange={(value) => handleButtonChange(index, 'url', value)}
                                onVariableChange={(variables) => handleButtonChange(index, 'example', variables.map(v => v.example))}
                                maxLength={2000}
                                placeholder={button.urlType === 'dynamic'
                                    ? "Ej: https://forms.gle/{{1}}.com"
                                    : "https://www.ejemplo.com"
                                }
                                urlType={button.urlType}
                                label="URL"
                                className="mb-0"
                                initialVariables={button.example ? button.example.map((v, i) => ({ number: i + 1, example: v })) : []}
                            />
                        </Space>
                    </div>
                ))}
            </div>

            {value.length === 0 && (
                <div className="text-center p-4 border rounded-lg border-dashed">
                    <Text type="secondary">
                        Agrega hasta 2 botones para que los usuarios puedan interactuar con tu mensaje
                    </Text>
                </div>
            )}
        </div>
    );
};

export default WhatsappButtonsEditor;