import React, { useState } from "react";
import { Input, Button, Tooltip, Typography, Space } from "antd";
import Plus from "@assets/icons/huge/plus.svg?react";
import Delete from "@assets/icons/huge/delete-stroke.svg?react";

const { TextArea } = Input;
const { Text } = Typography;
interface WhatsappTemplateEditorProps {
    value: string;
    onChange: (value: string) => void;
    onVariableChange?: (variables: Variable[]) => void;
    maxLength?: number;
    initialVariables?: string[];
}
interface Variable {
    number: number;
    example: string;
}
const WhatsappTemplateEditor = ({
    value,
    onChange,
    onVariableChange,
    initialVariables,
    maxLength = 1024,
}: WhatsappTemplateEditorProps) => {
    const [cursorPosition, setCursorPosition] = useState(0);
    const [variables, setVariables] = useState<Variable[]>(
        initialVariables?.map((v, i) => ({ number: i + 1, example: v })) || [],
    );

    const handleTextAreaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        onChange(e.target.value);
    };

    React.useEffect(() => {
        if (onVariableChange) {
            onVariableChange(variables);
        }
    }, [variables]);

    const addVariable = () => {
        const nextNumber = variables.length + 1;
        const before = value.substring(0, cursorPosition);
        const after = value.substring(cursorPosition);
        const newText = `${before}{{${nextNumber}}}${after}`;

        if (newText.length <= maxLength) {
            onChange(newText);
            setVariables([...variables, { number: nextNumber, example: "" }]);
        }
    };

    const handleTextAreaClick = (e: React.MouseEvent<HTMLTextAreaElement>) => {
        const target = e.target as HTMLTextAreaElement;
        setCursorPosition(target.selectionStart);
    };

    const updateVariableExample = (number: number, example: string) => {
        setVariables(
            variables.map((v) => (v.number === number ? { ...v, example } : v)),
        );
    };

    const removeVariable = (number: number) => {
        // Eliminar la variable y actualizar el texto
        const newText = value.replace(`{{${number}}}`, "");
        onChange(newText);

        // Eliminar el ejemplo y renumerar las variables restantes
        const newVariables = variables
            .filter((v) => v.number !== number)
            .map((v, index) => ({
                number: index + 1,
                example: v.example,
            }));

        setVariables(newVariables);

        // Actualizar todas las referencias en el texto
        let updatedText = newText;
        variables.forEach((v, index) => {
            if (v.number > number) {
                updatedText = updatedText.replace(
                    `{{${v.number}}}`,
                    `{{${index + 1}}}`,
                );
            }
        });

        onChange(updatedText);
    };

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                    <Text strong>Texto del mensaje</Text>
                    <Button
                        type="dashed"
                        icon={<Plus />}
                        onClick={addVariable}
                        className="flex items-center"
                    >
                        Agregar variable
                    </Button>
                </div>
                <Tooltip title="Caracteres disponibles">
                    <Text type="secondary">
                        {value?.length || 0}/{maxLength}
                    </Text>
                </Tooltip>
            </div>

            <TextArea
                value={value}
                onChange={handleTextAreaChange}
                onClick={handleTextAreaClick}
                // onSelect={(e) => setCursorPosition(e.target.selectionStart)}
                onSelect={(e) =>
                    setCursorPosition((e.target as HTMLTextAreaElement).selectionStart)
                }
                placeholder="Texto del mensaje de WhatsApp"
                autoSize={{ minRows: 3, maxRows: 7 }}
                maxLength={maxLength}
                className="font-mono"
            />

            {variables.length > 0 && (
                <div className="space-y-3 p-4 border rounded-lg">
                    <Text strong>Ejemplos de variables</Text>
                    {variables.map((variable) => (
                        <Space key={variable.number} className="flex items-center">
                            <Text>{`{{${variable.number}}}`}</Text>
                            <Input
                                placeholder="Ingresa un ejemplo"
                                value={variable.example}
                                onChange={(e) =>
                                    updateVariableExample(
                                        variable.number,
                                        e.target.value,
                                    )
                                }
                                className="w-64"
                            />
                            <Button
                                type="text"
                                danger
                                icon={<Delete />}
                                onClick={() => removeVariable(variable.number)}
                            />
                        </Space>
                    ))}
                </div>
            )}
        </div>
    );
};

export default WhatsappTemplateEditor;
