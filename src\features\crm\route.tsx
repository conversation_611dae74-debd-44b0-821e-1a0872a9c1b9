import { Route, Routes } from "react-router-dom";
import CrmDashboardPage from "./pages/dashboard";
import NotFoundPage from "./pages/not-found";
import ContactsListPage from "./pages/contacts/list";
import ContactDetailPage from "./pages/contacts/detail";
import OrdersListPage from "./pages/orders/list";
import PaymentsListPage from "./pages/payments/list";
import PaymentsDetailPage from "./pages/payments/detail";
import EventsListPage from "./pages/events/list";
import EventsDetailPage from "./pages/events/detail";
import OrdersDetailPage from "./pages/orders/detail";
import EventSchedulesListPage from "./pages/event-schedules/list";
import EventScheduleDetailPage from "./pages/event-schedules/detail";
import ActivitiesListPage from "./pages/activities/list";
import ActivityDetailPage from "./pages/activities/detail";
import CrmOutlet from "./outlet";
import { SubModuleProtectedRoute } from "../../routes/ProtectedOutlet/module-protected-outlet";

export default function CrmRoutes() {
    const REDIRECT_TO = "/crm";

    return (
        <Routes>
            <Route element={<CrmOutlet />}>
                {/** CRM Dashboard Route */}
                <Route path="" element={<CrmDashboardPage />} />

                {/** Contacts Routes */}
                <Route
                    element={
                        <SubModuleProtectedRoute
                            subModule="crm.user"
                            redirectTo={REDIRECT_TO}
                        />
                    }
                >
                    <Route path="contacts" element={<ContactsListPage />} />
                    <Route path="contacts/:cid" element={<ContactDetailPage />} />
                </Route>

                {/** Sales Routes */}
                <Route
                    element={
                        <SubModuleProtectedRoute
                            subModule="crm.order"
                            redirectTo={REDIRECT_TO}
                        />
                    }
                >
                    <Route path="orders" element={<OrdersListPage />} />
                    <Route path="orders/:oid" element={<OrdersDetailPage />} />
                </Route>

                {/** Payments Routes */}
                <Route
                    element={
                        <SubModuleProtectedRoute
                            subModule="crm.payment"
                            redirectTo={REDIRECT_TO}
                        />
                    }
                >
                    <Route path="payments" element={<PaymentsListPage />} />
                    <Route path="payments/:pid" element={<PaymentsDetailPage />} />
                </Route>

                {/** Activities Routes */}
                <Route
                    element={
                        <SubModuleProtectedRoute
                            subModule="crm.activity"
                            redirectTo={REDIRECT_TO}
                        />
                    }
                >
                    <Route path="activities" element={<ActivitiesListPage />} />
                    <Route path="activities/:aid" element={<ActivityDetailPage />} />
                </Route>

                {/** Events Routes */}
                <Route
                    element={
                        <SubModuleProtectedRoute
                            subModule="crm.event"
                            redirectTo={REDIRECT_TO}
                        />
                    }
                >
                    <Route path="events" element={<EventsListPage />} />
                    <Route path="events/:eid" element={<EventsDetailPage />} />
                </Route>

                {/** Event Schedules Routes */}
                <Route
                    element={
                        <SubModuleProtectedRoute
                            subModule="crm.eventschedule"
                            redirectTo={REDIRECT_TO}
                        />
                    }
                >
                    <Route
                        path="event-schedules"
                        element={<EventSchedulesListPage />}
                    />
                    <Route
                        path="event-schedules/:esid"
                        element={<EventScheduleDetailPage />}
                    />
                </Route>

                {/** Not Found Route */}
                <Route path="*" element={<NotFoundPage />} />
            </Route>
        </Routes>
    );
}
