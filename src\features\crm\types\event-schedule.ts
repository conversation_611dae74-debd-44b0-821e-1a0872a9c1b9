import { Dayjs } from "dayjs";
import { EventModality, EventStage } from "./event";
import { Template } from "@myTypes/template";

type AuditBaseType = {
    createdAt: string;
    updatedAt: string;
};

type Instructor = {
    name: string;
    iid: string;
};

export type EventSchedulePartnership = {
    key: string;
    pid: string;
    name: string;
    institution: string;
} & AuditBaseType;

export type FileBase = {
    fid: string;
    name: string;
    url: string;
    contentType: string;
};

export type EventScheduleCoverImage = FileBase;
export type EventScheduleThumbnail = FileBase;

export type PartnershipEnrollmentUrl = {
    pid: string;
    name: string;
    institution: string;
    enrollmentUrl: string;
};

export type EventSchedule = {
    key: string;
    esid: string;
    event: string;
    name: string;
    isGeneral: boolean;
    description?: string;
    thumbnail: EventScheduleThumbnail;
    coverImage: EventScheduleCoverImage;
    startDate: string;
    endDate: string;
    stage: EventStage;
    modality: EventModality;
    location: string;
    instructor?: Instructor;
    price: number;
    partnerships: EventSchedulePartnership[];
    enrollmentUrl: string;
    partnershipEnrollmentUrls: PartnershipEnrollmentUrl[];
    extEventLink?: string;
    whatsappTemplate: string;
    scheduledDatetimeEmail: string;
    scheduledDatetimeWhatsapp: string;
    whatsappDelayRange: number[];
    isWhatsappActive: boolean;
} & AuditBaseType;

export type RetrieveEventSchedule = Omit<EventSchedule, "whatsappTemplate"> & {
    whatsappTemplate: Template;
    emailsReminderAuto: boolean;
};

export type CreateEventScheduleFormBody = {
    event: string;
    startDate: string;
    endDate: string;
    partnerships: string[];
    stage: EventStage;
    isGeneral: boolean;
    isWhatsappActive: boolean;
    scheduledDatetimeWhatsapp: string | null;
    whatsappTemplate: string | null;
};

export type CreateEventScheduleFormValues = CreateEventScheduleFormBody & {
    completeInfo: boolean;
};

export type UpdateEventScheduleFormValues = {
    name: string;
    modality: string;
    stage: EventStage;
    partnerships: string[];
    description: string;
    rangeDate: [Dayjs, Dayjs];
    location: string;
    instructor: string;
    price: number;
    isGeneral: boolean;
    whatsappTemplate: string;
    scheduledDatetimeEmail: Dayjs;
    scheduledDatetimeWhatsapp: Dayjs;
    whatsappDelayRange: number[];
    isWhatsappActive: boolean;
    emailsReminderAuto: boolean;
};

export const DEFAULT_PAGE = 1;
export const DEFAULT_PAGE_SIZE = 10;

export type ListEventSchedulesQueryParams = {
    page?: number;
    pageSize?: number;
    search?: string;
    event?: string;
};
