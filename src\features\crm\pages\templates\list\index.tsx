import { useEffect, useMemo, useState } from "react";
import {
    Button,
    Checkbox,
    ConfigProvider,
    Dropdown,
    Empty,
    Form,
    Input,
    Modal,
    Pagination,
    Popover,
    Table,
    Typography,
} from "antd";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useSearchParams } from "react-router-dom";

const { Text } = Typography;
const { Search } = Input;

import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import Spinner from "@components/shared/atoms/Spinner";
// import { createTemplate, listTemplate, deleteTemplate } from "@services/portals/crm/template";

import Import from "@assets/icons/huge/import.svg?react";
import Plus from "@assets/icons/general/plus-white.svg?react";
import Reload from "@assets/icons/huge/reload.svg?react";
import Settings from "@assets/icons/huge/settings.svg?react";
import Trash from "@assets/icons/huge/trash-white.svg?react";
import DeleteStroke from "@assets/icons/huge/delete-stroke.svg?react";
import EditStroke from "@assets/icons/huge/edit-stroke.svg?react";
import MoreVertical from "@assets/icons/huge/more-vertical.svg?react";

import {
    CreateTemplateBody,
    CreateTemplateFormValues,
    Template,
} from "@/features/crm/types/template";
import {
    createTemplate,
    deleteTemplate,
    listTemplate,
} from "@/features/crm/services/portals/template";
import { AxiosError } from "axios";
import { useApiError } from "@hooks/use-api-error";
import CrmLayout from "@/features/crm/layout";

const COLUMN_OPTIONS = [
    { label: "Nombre", value: "name" },
    { label: "Categoría", value: "category" },
    { label: "Estado", value: "status" },
    { label: "Fecha de creación", value: "createdAt" },
];

export default function TemplateListPage() {

    const [modalOpen, setModalOpen] = useState(false);
    const [addForm] = Form.useForm<CreateTemplateFormValues>();
   

    // Modify handleRowAction to include delete functionality
  
   

    const { mutate } = useMutation({
        mutationFn: (values: CreateTemplateFormValues) => {
            const data: CreateTemplateBody = {
                ...values,
            };
            return createTemplate(data);
        },
        onSuccess: () => {
            addForm.resetFields();
            setModalOpen(false);
            refetch();
        },
        onError: () => {
            console.error("Error creating Template");
        },
    });

    const handleFormFinish = (values: CreateTemplateFormValues) => {
        mutate(values);
    };

    return (
        <>
            <CrmLayout>
                <div className="w-full h-full space-y-5">
                    <Modal
                        centered
                        className="max-w-4xl w-full"
                        open={modalOpen}
                        title={
                            <div className="w-full flex justify-center text-2xl py-4">
                                Agregar nueva plantilla
                            </div>
                        }
                        footer={false}
                        onCancel={() => setModalOpen(false)}
                    >
                        <Form
                            name="createTemplate"
                            layout="vertical"
                            form={addForm}
                            onFinish={handleFormFinish}
                        >
                            <Form.Item<CreateTemplateFormValues>
                                name="name"
                                label={
                                    <span className="font-semibold text-base">
                                        Nombre
                                    </span>
                                }
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            "Por favor, ingrese el nombre del programa",
                                    },
                                ]}
                            >
                                <Input
                                    placeholder="Ej. Manejo de Software"
                                    className="py-1"
                                />
                            </Form.Item>
                            <div className="grid grid-cols-2 gap-2">
                                <Button
                                    onClick={() => setModalOpen(false)}
                                    className="h-fit"
                                    size="large"
                                >
                                    Cancelar
                                </Button>
                                <Form.Item>
                                    <Button
                                        type="primary"
                                        htmlType="submit"
                                        className="h-fit"
                                        size="large"
                                        block
                                    >
                                        Guardar
                                    </Button>
                                </Form.Item>
                            </div>
                        </Form>
                    </Modal>
                    <div className="flex justify-between items-center">
                        <WelcomeBar helperText="Gestiona aquí las plantillas para mensajes de WhatsApp" />

                        <div className="flex gap-3">
                            <Button
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Import />}
                            >
                                Importar
                            </Button>
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Plus />}
                                onClick={() => setModalOpen(true)}
                            >
                                Agregar
                            </Button>
                        </div>
                    </div>
                    <div className="p-5 bg-white-full rounded-lg space-y-5">
                        <div className="flex justify-between items-center">
                            <Text className="text-black-medium text-2xl font-semibold">
                                Plantillas
                            </Text>

                            <div className="flex items-center gap-3">
                                <Search
                                    size="large"
                                    enterButton
                                    allowClear
                                    onSearch={(value, _, info) => {
                                        console.info(info?.source, value); // TODO: Implement search
                                    }}
                                />
                                <Button
                                    icon={<Reload />}
                                    size="large"
                                    type="text"
                                    onClick={() => refetch()}
                                />
                                <Popover
                                    content={
                                        <div className="p-2 space-y-3">
                                            <div className="uppercase text-black-medium font-medium">
                                                Mostrar/Ocultar Columnas
                                            </div>
                                            <div className="px-2">
                                                <Checkbox.Group
                                                    defaultValue={
                                                        INITIAL_CHECKED_VALUES
                                                    }
                                                    onChange={handleShowHideColumns}
                                                    name="columns"
                                                    className="flex flex-col gap-1"
                                                    options={COLUMN_OPTIONS}
                                                />
                                            </div>
                                        </div>
                                    }
                                    trigger={["click"]}
                                    placement="bottomRight"
                                >
                                    <Button
                                        icon={<Settings />}
                                        size="large"
                                        type="text"
                                    />
                                </Popover>
                            </div>
                        </div>
                    </div>
                </div>
            </CrmLayout>
        </>
    );
}
