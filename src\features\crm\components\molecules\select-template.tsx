import {
    useSyncTemplates,
    useTemplates,
    UseTemplatesQuery,
} from "@/features/crm/hooks/use-template";
import { Button, Select, SelectProps, Tooltip } from "antd";
import { ExternalLink } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";
import { useDebounce } from "@hooks/use-debounce";
import { Template } from "@myTypes/template";
import Reload from "@assets/icons/huge/reload.svg?react";

interface SelectTemplateProps extends Omit<SelectProps, "options"> {
    value?: string;
    onChange?: (value: string) => void;
    selectedTemplate?: Template;
}

export default function SelectTemplate({
    value,
    onChange,
    selectedTemplate,
    ...restProps
}: SelectTemplateProps) {
    const [query, setQuery] = useState<UseTemplatesQuery | null>(null);
    const debouncedQuery = useDebounce(query, 1000);
    // const [modalOpen, setModalOpen] = useState(false);

    const { mutate: syncTemplates, isPending: isSyncingTemplates } = useSyncTemplates();

    const { templates, isLoading, refetch } = useTemplates({
        page: 1,
        pageSize: 1000,
        query: {
            name: debouncedQuery?.name,
        },
    });

    // Crear opciones de Templateos
    const templateOptions: SelectProps["options"] =
        templates?.map((template) => ({
            value: template.tid,
            label: template.name,
            info: template,
        })) || [];

    const options = [...templateOptions];
    if (value && selectedTemplate) {
        const isValueInOptions = templateOptions.some(
            (option) => option.value === value,
        );
        if (!isValueInOptions) {
            options.unshift({
                value: value,
                label: selectedTemplate.name,
                info: selectedTemplate,
            });
        }
    }

    const handleSearch = (value: string) => {
        setQuery({ name: value });
    };

    const handleClear = () => {
        setQuery(null);
    };

    const handleSyncTemplates = () => {
        syncTemplates();
        refetch();
    };

    return (
        <div className="flex items-center gap-2">
            {/* TODO: Modal para crear template */}
            {/* <Modal
                title={
                    <div className="text-lg font-semibold text-center">
                        Crear Templateo
                    </div>
                }
                footer={false}
                open={modalOpen}
                centered
                onCancel={() => {
                    setModalOpen(false);
                }}
            >
                <CreateTemplateForm handleCloseModal={handleCloseModal} />
            </Modal> */}
            <Select
                {...restProps}
                value={value}
                onChange={onChange}
                onSearch={handleSearch}
                onClear={handleClear}
                options={options}
                placeholder="Buscar por nombre"
                optionRender={(option) => (
                    <div className="flex justify-between items-center">
                        <div className="flex flex-col">
                            <span>{option.data.label}</span>
                        </div>

                        {option.data.info?.extReference && (
                            <Link
                                to={`https://crm.tokechat.net/flows/${option.data.info.extReference}`}
                                title="View Template"
                                target="_blank"
                            >
                                <ExternalLink size={14} />
                            </Link>
                        )}
                    </div>
                )}
                loading={isLoading}
                filterOption={false}
                allowClear
                showSearch
                // dropdownRender={(menu) => (
                //     <>
                //         {menu}
                //         <Divider className="my-1" />
                //         <div className="flex justify-between items-center px-2">
                //             <p className="text-sm text-gray-700 font-medium">
                //                 ¿No encuentras el Templateo?
                //             </p>
                //             <Button
                //                 size="small"
                //                 type="primary"
                //                 icon={<Plus size={12} />}
                //                 onClick={() => setModalOpen(true)}
                //             >
                //                 Agregar
                //             </Button>
                //         </div>
                //     </>
                // )}
            />
            <Tooltip title="Actualizar lista de plantillas">
                <Button onClick={handleSyncTemplates} disabled={isSyncingTemplates}>
                    <Reload />
                    <span className="sr-only">Actualizar lista de plantillas</span>
                </Button>
            </Tooltip>
        </div>
    );
}
