import { useMutation, useQuery } from "@tanstack/react-query";
import { listTemplate, syncTemplates } from "../services/portals/template";
import { AxiosError } from "axios";
import { useApiError } from "@hooks/use-api-error";
import { App } from "antd";

export type UseTemplatesQuery = Partial<{
    name: string;
}>;

type UseTemplatesProps = {
    page?: number;
    pageSize?: number;
    query?: UseTemplatesQuery;
    enabled?: boolean;
};

export const useTemplates = ({ query, ...rest }: UseTemplatesProps = {}) => {
    const { data, isLoading, isError, isFetching, refetch } = useQuery({
        queryKey: ["templates", query, rest],
        queryFn: () =>
            listTemplate({
                ...query,
                ...rest,
            }),
        enabled: rest.enabled || true,
        refetchOnWindowFocus: false,
    });

    const { count: COUNT, results: templates } = data || {
        count: 0,
        results: [],
    };

    return {
        refetch,
        isLoading: isLoading || isFetching,
        isError,
        templates,
        COUNT,
    };
};

type UseSyncTemplatesProps = {
    onSuccess?: () => void;
    onError?: (error: AxiosError) => void;
};

export const useSyncTemplates = ({
    onSuccess,
    onError,
}: UseSyncTemplatesProps = {}) => {
    const { message } = App.useApp();

    const { handleError } = useApiError({
        title: "Inténtalo de nuevo más tarde",
        genericMessage:
            "No se pudo sincronizar las plantillas, inténtalo de nuevo más tarde.",
    });

    return useMutation({
        mutationFn: () => {
            return syncTemplates();
        },
        onSuccess: () => {
            message.success("Las plantillas se han sincronizado correctamente");
            onSuccess?.();
        },
        onError: (error: AxiosError) => {
            handleError(error);
            onError?.(error);
        },
    });
};
