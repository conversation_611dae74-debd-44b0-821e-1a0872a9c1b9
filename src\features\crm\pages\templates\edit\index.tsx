import { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";
import {
    <PERSON>readcrumb,
    Button,
    Form,
    Image,
    Input,
    Tag,
    Typography,
    Upload,
    message,
    notification,
} from "antd";
import ImgCrop from "antd-img-crop";
import { useMutation, useQuery } from "@tanstack/react-query";
import type { UploadFile, GetProp, UploadProps } from "antd";

const { Text } = Typography;
const { Dragger } = Upload;

import Spinner from "@components/shared/atoms/Spinner";

import Save from "@assets/icons/general/save-stroke.svg?react";
import CloudUpload from "@assets/shapes/cloud-upload.svg?react";

import {
    retrieveTemplate,
    updateTemplate,
} from "@/features/crm/services/portals/template";
import { openErrorNotification } from "@lib/notification";
import {
    TemplateStatus,
    TemplateStatusLabel,
    PartialUpdateTemplateBody,
    TemplateButton,
} from "@myTypes/template";
import { getBase64 } from "@lib/helpers";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import CrmLayout from "@/features/crm/layout";
import WhatsappTemplateEditor from "@/features/crm/components/molecules/templates/WhatsappTemplateEditor";
import WhatsappButtonsEditor from "@/features/crm/components/molecules/templates/WhatsappButtonsEditor";
import WhatsAppMessagePreview from "@/features/crm/components/molecules/templates/WhatsappMessagePreview";

type FileType = Parameters<GetProp<UploadProps, "beforeUpload">>[0];

type DraggerProps = {
    customRequest: UploadProps["customRequest"];
    onRemove?: UploadProps["onRemove"];
    onPreview?: UploadProps["onPreview"];
    fileList?: UploadProps<FileType>["fileList"];
};

export default function TemplateEditPage() {
    const [messageApi, messageHolder] = message.useMessage();
    const [notificationApi, notificationHolder] = notification.useNotification();
    const { tid } = useParams<{ tid: string }>();
    const [form] = Form.useForm<PartialUpdateTemplateBody>();

    const { isLoading, isError, data, refetch } = useQuery({
        queryKey: ["template", tid],
        queryFn: () => retrieveTemplate(tid as string),
        enabled: tid !== undefined,
        refetchOnWindowFocus: false,
    });

    const updateMutation = useMutation({
        mutationFn: (data: PartialUpdateTemplateBody) => {
            return updateTemplate(tid as string, data);
        },
        onSuccess: () => {
            messageApi.success("Plantilla actualizada correctamente");
            refetch();
        },
        onError: (error: Error) => {
            openErrorNotification(
                "Error al actualizar la plantilla",
                error.message,
                notificationApi,
            );
        },
    });

    const [fileList, setFileList] = useState<UploadFile<FileType>[]>([]);
    const [previewOpen, setPreviewOpen] = useState(false);

    useEffect(() => {
        if (data && data.headerImage) {
            const currentFile = {
                uid: data.headerImage.fid,
                name: data.headerImage.name,
                status: "done",
                url: data.headerImage.url,
            };
            setFileList([currentFile as UploadFile<FileType>]);
        }
    }, [data, form]);

    const handleSave = () => {
        form.submit();
    };

    // 1. Primero, usa Form.useWatch para observar los cambios en los campos
    const bodyText = Form.useWatch("bodyText", form);
    const positionalParamsExample = Form.useWatch("positionalParamsExample", form);
    const buttons = Form.useWatch("buttons", form);

    const handleFormFinish = (values: PartialUpdateTemplateBody) => {
        if (data?.headerImage && fileList.length === 0) {
            values.deleteHeaderImage = true;
        }
        if (form.getFieldValue("positionalParamsExample")) {
            values.positionalParamsExample = form.getFieldValue(
                "positionalParamsExample",
            );
        }
        updateMutation.mutate(values);
    };

    const draggerProps: DraggerProps = {
        customRequest: async (options) => {
            const { file, onSuccess, onError } = options;
            try {
                if (typeof onSuccess === "function") {
                    const base64file = await getBase64(file as FileType);
                    const uploadFile: UploadFile<FileType> =
                        file as UploadFile<FileType>;
                    uploadFile.url = base64file;

                    form.setFieldsValue({
                        headerImageFile: [uploadFile],
                    });
                    setFileList([uploadFile]);
                    onSuccess(uploadFile);
                }
            } catch (error) {
                typeof onError === "function" && onError(error as Error);
            }
        },
        onRemove: () => {
            form.setFieldsValue({
                headerImageFile: undefined,
            });
            setFileList([]);
        },
        onPreview: async () => {
            setPreviewOpen(true);
        },
        fileList,
    };

    function setBodyText(value: string): void {
        form.setFieldsValue({ bodyText: value });
    }

    function setPositionalParamsExample(value: string[]): void {
        form.setFieldsValue({ positionalParamsExample: value });
    }

    function setButtons(value: TemplateButton[]): void {
        form.setFieldsValue({ buttons: value });
    }

    const replaceVariables = (text: string, positionalParamsExample: string[]) => {
        return text.replace(/{{(\d+)}}/g, (_, number) => {
            return positionalParamsExample[number - 1];
        });
    };

    return (
        <>
            {messageHolder}
            {notificationHolder}
            <CrmLayout>
                <div className="max-w-7xl w-full h-full space-y-5">
                    <div className="flex justify-between items-center">
                        <WelcomeBar helperText="Gestiona aquí las plantillas para mensajes de WhatsApp" />
                        <div className="flex gap-3">
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Save />}
                                disabled={isError}
                                onClick={handleSave}
                            >
                                Guardar
                            </Button>
                        </div>
                    </div>
                    {isLoading ? (
                        <Spinner />
                    ) : (
                        <>
                            <div className="p-5 bg-white-full rounded-lg space-y-5 shadow-sm">
                                <div className="flex items-center">
                                    <Breadcrumb
                                        separator=">"
                                        items={[
                                            {
                                                title: (
                                                    <Link
                                                        to="/crm/templates"
                                                        className="text-base"
                                                    >
                                                        Plantillas
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Link
                                                        to={`/crm/templates/${tid}`}
                                                        className="text-base"
                                                    >
                                                        {data?.name}
                                                    </Link>
                                                ),
                                            },
                                            {
                                                title: (
                                                    <Text className="text-base">
                                                        Editar
                                                    </Text>
                                                ),
                                            },
                                        ]}
                                    />
                                </div>
                            </div>
                            <div className="space-y-5">
                                <Form
                                    name="templateEdit"
                                    layout="vertical"
                                    form={form}
                                    initialValues={data}
                                    onFinish={handleFormFinish}
                                >
                                    <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
                                        <div className="bg-white-full col-span-4 p-5 rounded-lg shadow-sm">
                                            <p className="text-gray-400 font-semibold text-sm">
                                                INFORMACIÓN DE LA PLANTILLA
                                            </p>
                                            <Form.Item<PartialUpdateTemplateBody>
                                                name="name"
                                                label={
                                                    <span className="font-semibold text-base">
                                                        Nombre de la Plantilla
                                                    </span>
                                                }
                                                rules={[
                                                    {
                                                        required: true,
                                                        message:
                                                            "Por favor, ingrese el nombre de la plantilla",
                                                    },
                                                ]}
                                            >
                                                <Input
                                                    placeholder="Ej. Bienvenida al curso"
                                                    className="py-2"
                                                />
                                            </Form.Item>
                                            <div>
                                                <p className="text-gray-400 font-semibold text-sm">
                                                    IMAGEN DE CABECERA (OPCIONAL)
                                                </p>
                                                <Form.Item<PartialUpdateTemplateBody>
                                                    name="headerImageFile"
                                                    label={
                                                        <span className="font-semibold text-base">
                                                            Imagen de cabecera
                                                        </span>
                                                    }
                                                    valuePropName="listFile"
                                                    getValueFromEvent={(e) => {
                                                        return e?.fileList;
                                                    }}
                                                >
                                                    <ImgCrop aspect={16 / 9}>
                                                        <Dragger
                                                            {...draggerProps}
                                                            listType="picture"
                                                            maxCount={1}
                                                            multiple={false}
                                                        >
                                                            <div className="flex flex-col justify-center items-center">
                                                                <CloudUpload />
                                                                <Text className="font-medium text-black-full">
                                                                    Arrastre una imagen
                                                                    o haga click aquí
                                                                </Text>
                                                                <Text className="text-xs text-black-medium">
                                                                    Solo una imagen
                                                                    (Máx. 2MB)
                                                                </Text>
                                                            </div>
                                                        </Dragger>
                                                    </ImgCrop>
                                                </Form.Item>
                                                <Image
                                                    wrapperStyle={{ display: "none" }}
                                                    preview={{
                                                        visible: previewOpen,
                                                        onVisibleChange: setPreviewOpen,
                                                    }}
                                                    src={fileList[0]?.url}
                                                />
                                            </div>
                                            <Form.Item<PartialUpdateTemplateBody>
                                                name="bodyText"
                                                label={
                                                    <span className="font-semibold text-base">
                                                        Texto del mensaje
                                                    </span>
                                                }
                                                rules={[
                                                    {
                                                        required: true,
                                                        message:
                                                            "Por favor, ingrese el texto del mensaje",
                                                    },
                                                    {
                                                        max: 1024,
                                                        message:
                                                            "El texto no puede superar los 1024 caracteres",
                                                    },
                                                ]}
                                            >
                                                <WhatsappTemplateEditor
                                                    value={data?.bodyText || ""}
                                                    onChange={setBodyText}
                                                    maxLength={1024}
                                                    initialVariables={
                                                        data?.positionalParamsExample ||
                                                        []
                                                    }
                                                    onVariableChange={(variables) => {
                                                        setPositionalParamsExample(
                                                            variables.map(
                                                                (v) => v.example,
                                                            ),
                                                        );
                                                    }}
                                                />
                                            </Form.Item>
                                            <Form.Item<PartialUpdateTemplateBody>
                                                name="buttons"
                                                label={
                                                    <span className="font-semibold text-base">
                                                        Botones (Opcional)
                                                    </span>
                                                }
                                            >
                                                <WhatsappButtonsEditor
                                                    onChange={(buttons) => {
                                                        setButtons(buttons);
                                                    }}
                                                    value={data?.buttons || []}
                                                />
                                            </Form.Item>
                                        </div>
                                        <div className="col-span-2 space-y-6">
                                            <div className="bg-white-full p-5 rounded-lg shadow-sm">
                                                <p className="text-gray-400 font-semibold text-sm">
                                                    ESTADO DE LA PLANTILLA
                                                </p>
                                                <div>
                                                    <Tag
                                                        color={
                                                            data?.status ===
                                                            TemplateStatus.APPROVED
                                                                ? "green"
                                                                : data?.status ===
                                                                    TemplateStatus.REJECTED
                                                                  ? "red"
                                                                  : data?.status ===
                                                                      TemplateStatus.DRAFT
                                                                    ? "blue"
                                                                    : "orange"
                                                        }
                                                        className="rounded-full px-3"
                                                        bordered={false}
                                                    >
                                                        {
                                                            TemplateStatusLabel[
                                                                data?.status ||
                                                                    TemplateStatus.DRAFT
                                                            ]
                                                        }
                                                    </Tag>
                                                </div>
                                            </div>
                                            <div className="h-full">
                                                <div className="py-4 px-5 bg-white-full rounded-lg shadow-sm cursor-pointer">
                                                    <p className="text-gray-400 font-semibold text-sm">
                                                        VISTA PREVIA
                                                    </p>
                                                </div>
                                                <WhatsAppMessagePreview
                                                    imageUrl={data?.headerImage?.url}
                                                    bodyText={replaceVariables(
                                                        bodyText ||
                                                            data?.bodyText ||
                                                            "",
                                                        positionalParamsExample ||
                                                            data?.positionalParamsExample ||
                                                            [],
                                                    )}
                                                    buttons={
                                                        buttons?.map(
                                                            (button) => button.text,
                                                        ) ||
                                                        data?.buttons?.map(
                                                            (button) => button.text,
                                                        )
                                                    }
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </Form>
                            </div>
                        </>
                    )}
                </div>
            </CrmLayout>
        </>
    );
}
