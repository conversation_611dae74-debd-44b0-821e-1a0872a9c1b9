import CrmLayout from "@/features/crm/layout";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import { Badge, Button, Input, Modal, Pagination, Typography } from "antd";
import { Plus, SlidersHorizontal } from "lucide-react";
import { useState, useMemo } from "react";

import CreateEventScheduleForm from "@/features/crm/components/organisms/create-event-schedule-form";
import { useEventSchedules } from "@/features/crm/hooks/use-event-schedule";
import EventSchedulesTable from "@/features/crm/components/organisms/event-schedules-table";
import { useSearchParams } from "react-router-dom";
import {
    DEFAULT_PAGE,
    DEFAULT_PAGE_SIZE,
    ListEventSchedulesQueryParams,
} from "@/features/crm/types/event-schedule";
import EventSchedulesFilters from "@/features/crm/components/organisms/event-schedules-filters";

const { Text } = Typography;
const { Search } = Input;

export default function EventSchedulesListPage() {
    const [searchParams, setSearchParams] = useSearchParams();
    const page = Number(searchParams.get("page")) || DEFAULT_PAGE;
    const pageSize = Number(searchParams.get("pageSize")) || DEFAULT_PAGE_SIZE;
    const search = searchParams.get("search") || undefined;
    const stage = searchParams.get("stage") || "";
    const isGeneral = searchParams.get("isGeneral");
    const partnerships = searchParams.get("partnerships") || "";
    const instructor = searchParams.get("instructor") || "";
    const startDate = searchParams.get("startDate") || "";
    const endDate = searchParams.get("endDate") || "";
    const filterDateBy = searchParams.get("filterDateBy") || "";

    const queryParams: ListEventSchedulesQueryParams = useMemo(
        () => ({
            page: Number(page),
            pageSize: Number(pageSize),
            ...(search ? { search } : {}),
            ...(stage ? { stage } : {}),
            ...(isGeneral !== null ? { isGeneral: isGeneral === "true" } : {}),
            ...(partnerships ? { partnerships } : {}),
            ...(instructor ? { instructor } : {}),
            ...(startDate ? { startDate } : {}),
            ...(endDate ? { endDate } : {}),
            ...(filterDateBy ? { filterDateBy } : {}),
        }),
        [
            page,
            pageSize,
            search,
            stage,
            isGeneral,
            partnerships,
            instructor,
            startDate,
            endDate,
            filterDateBy,
        ],
    );

    const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState<boolean>(false);
    const [activeTag, setActiveTag] = useState("Todos");
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const tags = ["Todos", "Culminados", "Planeados"];

    const { count, eventSchedules } = useEventSchedules({ queryParams });

    const handleSetPage = (page: number, pageSize: number) => {
        setSearchParams((prev) => {
            prev.set("page", page.toString());
            prev.set("pageSize", pageSize.toString());
            return prev;
        });
    };

    const handleSetSerachQuery = (value: string) => {
        setSearchParams((prev) => {
            prev.set("search", value);
            return prev;
        });
    };

    const clearFilters = () => {
        setSearchParams((prev) => {
            prev.delete("stage");
            prev.delete("isGeneral");
            prev.delete("partnerships");
            prev.delete("instructor");
            prev.delete("startDate");
            prev.delete("endDate");
            prev.delete("filterDateBy");
            prev.set("page", "1");
            return prev;
        });
    };

    return (
        <CrmLayout>
            <div className="w-full h-full space-y-5">
                <div className="flex justify-between items-center">
                    <WelcomeBar helperText="Gestiona aquí los horarios de eventos" />
                    <div className="flex gap-3">
                        <Button
                            type="primary"
                            size="large"
                            style={{ fontSize: 16 }}
                            icon={<Plus />}
                            onClick={() => setIsCreateModalOpen(true)}
                        >
                            Agregar
                        </Button>
                        <Modal
                            centered
                            open={isCreateModalOpen}
                            onCancel={() => setIsCreateModalOpen(false)}
                            footer={false}
                            title={
                                <div className="w-full flex justify-center text-xl py-1">
                                    Agregar nuevo Horario
                                </div>
                            }
                        >
                            <CreateEventScheduleForm
                                closeModal={() => setIsCreateModalOpen(false)}
                            />
                        </Modal>
                    </div>
                </div>
                <div className="p-5 bg-white-full rounded-lg space-y-5">
                    <div className="flex flex-col lg:flex-row justify-between items-center">
                        <Text className="text-black-medium text-2xl font-semibold">
                            Horarios de Eventos{" "}
                            <Badge count={count} color="blue" size="default" />
                        </Text>
                        <Search
                            size="large"
                            placeholder="Buscar por nombre, descripción o ubicación de evento"
                            onSearch={(value) => {
                                handleSetSerachQuery(value);
                            }}
                            enterButton
                            allowClear
                            className="max-w-screen-sm"
                        />
                        <div className="flex items-center gap-3">
                            <div className="relative">
                                <Button
                                    icon={<SlidersHorizontal size={16} />}
                                    onClick={() => setIsFilterDrawerOpen(true)}
                                >
                                    Filtros
                                </Button>
                                {/* Active filters indicator */}
                                {(stage ||
                                    isGeneral ||
                                    partnerships ||
                                    instructor ||
                                    startDate ||
                                    endDate ||
                                    filterDateBy) && (
                                    <Badge
                                        count={
                                            [
                                                stage,
                                                isGeneral,
                                                partnerships,
                                                instructor,
                                                startDate || endDate,
                                                filterDateBy,
                                            ].filter(Boolean).length
                                        }
                                        size="small"
                                        className="absolute -top-1 -right-1"
                                    />
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Active Filters Summary */}
                    {(stage ||
                        isGeneral ||
                        partnerships ||
                        instructor ||
                        startDate ||
                        endDate ||
                        filterDateBy) && (
                        <div className="flex flex-wrap items-center gap-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                            <Typography.Text
                                type="secondary"
                                className="text-sm font-medium"
                            >
                                Filtros activos:
                            </Typography.Text>
                            {stage && <Badge count="Etapa" color="blue" />}

                            {isGeneral && <Badge count="Tipo de evento" color="blue" />}

                            {partnerships && <Badge count="Alianza" color="blue" />}

                            {instructor && <Badge count="Instructor" color="blue" />}

                            {(startDate || endDate) && (
                                <Badge count="Fechas" color="green" />
                            )}

                            <Button
                                type="link"
                                size="small"
                                onClick={clearFilters}
                                className="text-blue-600 hover:text-blue-800 p-0 h-auto"
                            >
                                Limpiar todos
                            </Button>
                        </div>
                    )}
                </div>

                <div className="hidden items-center gap-2 p-3 bg-gray-50 rounded-lg shadow-sm">
                    {tags.map((tag) => (
                        <button
                            key={tag}
                            onClick={() => setActiveTag(tag)}
                            className={`px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 ${
                                activeTag === tag
                                    ? "bg-blue-500 text-white-full shadow-md"
                                    : "bg-white text-gray-600 hover:bg-gray-100"
                            }`}
                        >
                            {tag}
                        </button>
                    ))}
                </div>
                <EventSchedulesTable eventSchedules={eventSchedules} />
                <div className="flex justify-between items-center p-4 bg-white-full rounded-lg shadow-sm">
                    <Text type="secondary">
                        {eventSchedules.length} de {count} Horarios de Eventos
                    </Text>
                    <Pagination
                        current={page}
                        pageSize={pageSize}
                        total={count}
                        onChange={handleSetPage}
                        showSizeChanger
                    />
                </div>
            </div>

            {/* Filters Drawer */}
            <EventSchedulesFilters
                isOpen={isFilterDrawerOpen}
                onClose={() => setIsFilterDrawerOpen(false)}
                onClearFilters={clearFilters}
            />
        </CrmLayout>
    );
}
