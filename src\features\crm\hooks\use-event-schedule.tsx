import { useMutation, useQuery } from "@tanstack/react-query";
import {
    CreateEventScheduleFormBody,
    ListEventSchedulesQueryParams,
} from "@/features/crm/types/event-schedule";
import {
    createEventSchedule,
    listEventSchedules,
} from "@/features/crm/services/portals/event-schedule";
import { type AxiosError } from "axios";
import { useApiError } from "@hooks/use-api-error";

type UseEventSchedulesProps = {
    queryParams?: ListEventSchedulesQueryParams;
};

export const useEventSchedules = ({ queryParams }: UseEventSchedulesProps = {}) => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["event-schedules", queryParams],
        queryFn: () => listEventSchedules(queryParams),
        refetchOnWindowFocus: false,
    });

    const { count, results: eventSchedules } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading,
        isError,
        eventSchedules,
        count,
    };
};

type UseCreateEventScheduleProps = {
    onSuccess?: () => void;
    onError?: (error: AxiosError) => void;
};

export const useCreateEventSchedule = ({
    onSuccess,
    onError,
}: UseCreateEventScheduleProps = {}) => {
    const { handleError } = useApiError({
        title: "Error al crear horario de evento",
    });

    return useMutation({
        mutationFn: (newEventSchedule: CreateEventScheduleFormBody) =>
            createEventSchedule(newEventSchedule),
        onSuccess,
        onError: (error: AxiosError) => {
            handleError(error);
            onError?.(error);
        },
    });
};

// Re-export enrollment hook for convenience
export { useEventScheduleEnrollments } from "./use-event-schedule-enrollment";
