import { useEffect, useState } from 'react';
import Plus from "@assets/icons/huge/plus.svg?react";
import { Button, Form, Input, Space, Typography } from 'antd';

const { Text } = Typography;

interface URLVariableInputProps {
    value: string;
    onChange: (value: string) => void;
    onVariableChange?: (variables: Variable[]) => void;
    maxLength?: number;
    placeholder?: string;
    urlType?: string;
    label?: string;
    className?: string;
    initialVariables?: Variable[];
}


interface Variable {
    number: number;
    example: string;
}

const URLVariableInput = ({
    value,
    onChange,
    onVariableChange,
    maxLength = 2000,
    placeholder,
    urlType,
    label,
    className,
    initialVariables
}: URLVariableInputProps) => {
    const [cursorPosition, setCursorPosition] = useState(0);
    const [variables, setVariables] = useState<Variable[]>(
        initialVariables?.map((v, i) => ({ number: i + 1, example: v.example })) || []
    );

    const MAX_VARIABLES = 1;
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        onChange(e.target.value);
    };

    useEffect(() => {
        if (onVariableChange) {
            onVariableChange(variables);
        }
    }, [variables]);

    const addVariable = () => {
        if (variables.length >= MAX_VARIABLES) return;
        const nextNumber = variables.length + 1;
        const before = value?.substring(0, cursorPosition) || '';
        const after = value?.substring(cursorPosition) || '';
        const newText = `${before}{{${nextNumber}}}${after}`;

        if (newText.length <= maxLength) {
            onChange(newText);
            setVariables([...variables, { number: nextNumber, example: '' }]);
        }
    };

    const handleInputClick = (e: React.MouseEvent<HTMLInputElement>) => {
        setCursorPosition((e.target as HTMLInputElement).selectionStart!);
    };

    const updateVariableExample = (number: number, example: string) => {
        setVariables(variables.map(v =>
            v.number === number ? { ...v, example } : v
        ));
    };

    const removeVariable = (number: number) => {
        const newText = value.replace(`{{${number}}}`, '');
        onChange(newText);

        const newVariables = variables
            .filter(v => v.number !== number)
            .map((v, index) => ({
                number: index + 1,
                example: v.example
            }));

        setVariables(newVariables);

        let updatedText = newText;
        variables.forEach((v, index) => {
            if (v.number > number) {
                updatedText = updatedText.replace(
                    `{{${v.number}}}`,
                    `{{${index + 1}}}`
                );
            }
        });

        onChange(updatedText);
    };

    return (
        <div className="space-y-2">
            <Form.Item
                label={label}
                className={className}
                help={`${value?.length || 0}/${maxLength} caracteres`}
            >
                <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-2">
                        <Space.Compact className="w-full">
                            <Input
                                value={value}
                                onChange={handleInputChange}
                                onClick={handleInputClick}
                                onSelect={(e) => setCursorPosition((e.target as HTMLInputElement).selectionStart!)}
                                maxLength={maxLength}
                                placeholder={placeholder}
                            />
                        </Space.Compact>
                        {urlType === 'dynamic' && (
                            <Button
                                type="dashed"
                                icon={<Plus className="w-4 h-4" />}
                                onClick={addVariable}
                                className="flex items-center gap-2"
                                disabled={variables.length >= MAX_VARIABLES}
                            >
                                <span>Variable</span>
                            </Button>
                        )}
                    </div>

                    {variables.length > 0 && urlType === 'dynamic' && (
                        <div className="space-y-3 p-4 border rounded-lg">
                            <Text className="font-medium block mb-2">Ejemplos de variables</Text>
                            <div className="space-y-2">
                                {variables.map((variable) => (
                                    <div key={variable.number} className="flex items-center gap-2">
                                        <Text className="min-w-[50px]">{`{{${variable.number}}}`}</Text>
                                        <Input
                                            placeholder="Ingresa un ejemplo"
                                            value={variable.example}
                                            onChange={(e) => updateVariableExample(variable.number, e.target.value)}
                                            className="flex-1"
                                        />
                                        <Button
                                            type='dashed'

                                            onClick={() => removeVariable(variable.number)}
                                            className="text-red-500 hover:text-red-600"
                                        >
                                            Eliminar
                                        </Button>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </Form.Item>
        </div>
    );
};

export default URLVariableInput;